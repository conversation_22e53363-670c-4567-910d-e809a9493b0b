package main

import (
	"context"
	"fmt"
	"lumen_link/internal/core/dht"
	"lumen_link/internal/core/dht/factory"
	"lumen_link/pkg/config"
	"lumen_link/pkg/utils"
	"os"
	"time"

	"go.uber.org/zap"
)

func main() {
	// 创建logger
	logger, _ := zap.NewDevelopment()
	defer logger.Sync()

	// 创建上下文
	ctx, cancel := context.WithCancel(context.Background())
	defer cancel()

	// 加载全局安全配置
	securityConfig := config.DefaultSecurityConfig()

	// 设置全局哈希算法
	algo := securityConfig.GetHashAlgorithm()
	if err := utils.SetGlobalHashAlgorithm(algo); err != nil {
		logger.Warn("Unsupported hash algorithm, using default instead", zap.String("algorithm", algo), zap.Error(err))
	}

	// 创建DHT工厂
	dhtFactory := factory.NewFactory(logger)

	// 创建增强型DHT配置
	enhancedConfig := config.DefaultEnhancedDHTConfig()

	// 创建第一个DHT节点
	logger.Info("Creating first DHT node...")
	dhtNode1, err := dhtFactory.CreateDHTWithEnhancedFeatures(ctx, enhancedConfig)
	if err != nil {
		logger.Fatal("Failed to create first DHT node", zap.Error(err))
	}

	// 获取基础DHT
	baseDHT1, ok := dhtNode1.BaseDHT.(*dht.DHT)
	if !ok {
		logger.Fatal("Failed to get base DHT for first node")
	}

	// 启动第一个DHT服务
	err = baseDHT1.Start(ctx, "127.0.0.1:8001", &config.Config{
		Network: config.NetworkConfig{
			DHT: enhancedConfig.DHTConfig,
		},
	})
	if err != nil {
		logger.Fatal("Failed to start first DHT service", zap.Error(err))
	}
	defer baseDHT1.Stop()

	logger.Info("First DHT node started", zap.String("nodeID", fmt.Sprintf("%x", baseDHT1.GetLocalNode().ID)))

	// 创建第二个DHT节点
	logger.Info("Creating second DHT node...")
	dhtNode2, err := dhtFactory.CreateDHTWithEnhancedFeatures(ctx, enhancedConfig)
	if err != nil {
		logger.Fatal("Failed to create second DHT node", zap.Error(err))
	}

	// 获取基础DHT
	baseDHT2, ok := dhtNode2.BaseDHT.(*dht.DHT)
	if !ok {
		logger.Fatal("Failed to get base DHT for second node")
	}

	// 启动第二个DHT服务
	err = baseDHT2.Start(ctx, "127.0.0.1:8002", &config.Config{
		Network: config.NetworkConfig{
			DHT: enhancedConfig.DHTConfig,
		},
	})
	if err != nil {
		logger.Fatal("Failed to start second DHT service", zap.Error(err))
	}
	defer baseDHT2.Stop()

	logger.Info("Second DHT node started", zap.String("nodeID", fmt.Sprintf("%x", baseDHT2.GetLocalNode().ID)))

	// 测试数据存储和检索
	testKey := []byte("test_key")
	testValue := []byte("test_value")

	// 在第一个节点上存储数据
	logger.Info("Storing data on first node...")
	err = baseDHT1.Store(ctx, testKey, testValue)
	if err != nil {
		logger.Error("Failed to store data on first node", zap.Error(err))
	} else {
		logger.Info("Data stored successfully on first node")
	}

	// 等待一段时间，让数据传播
	time.Sleep(2 * time.Second)

	// 从第二个节点检索数据
	logger.Info("Retrieving data from second node...")
	retrievedValue, err := baseDHT2.Retrieve(ctx, testKey)
	if err != nil {
		logger.Error("Failed to retrieve data from second node", zap.Error(err))
	} else {
		logger.Info("Data retrieved successfully from second node", 
			zap.String("expected", string(testValue)), 
			zap.String("actual", string(retrievedValue)))
		
		// 验证数据
		if string(retrievedValue) == string(testValue) {
			logger.Info("✅ Test PASSED: Retrieved value matches stored value")
		} else {
			logger.Error("❌ Test FAILED: Retrieved value does not match stored value")
		}
	}

	// 测试查找最近节点
	logger.Info("Finding closest nodes...")
	nodes, err := baseDHT1.FindClosestNodes(ctx, "test_key", 10)
	if err != nil {
		logger.Error("Failed to find closest nodes", zap.Error(err))
	} else {
		logger.Info("Found closest nodes", zap.Int("count", len(nodes)))
		for i, node := range nodes {
			logger.Info(fmt.Sprintf("Node %d", i+1), zap.String("id", fmt.Sprintf("%x", node.ID)))
		}
	}

	// 测试提供者功能
	logger.Info("Testing provider functionality...")
	
	// 添加提供者
	provider := &dht.Provider{
		ID:             baseDHT1.GetLocalNode().ID,
		Addr:           "127.0.0.1:8001",
		LastSeen:       time.Now().Unix(),
		StabilityScore: 1.0,
	}
	
	baseDHT1.AddProvider("test_resource", provider)
	logger.Info("Provider added for test_resource")
	
	// 获取本地提供者
	providers := baseDHT1.GetLocalProviders("test_resource")
	logger.Info("Local providers retrieved", zap.Int("count", len(providers)))
	
	// 查找提供者
	foundProviders, err := baseDHT1.FindProviders(ctx, "test_resource")
	if err != nil {
		logger.Error("Failed to find providers", zap.Error(err))
	} else {
		logger.Info("Found providers", zap.Int("count", len(foundProviders)))
		if len(foundProviders) > 0 {
			logger.Info("✅ Test PASSED: Provider functionality works")
		} else {
			logger.Error("❌ Test FAILED: No providers found")
		}
	}

	// 列出所有键
	keys := baseDHT1.ListAllKeys()
	logger.Info("All keys in DHT", zap.Strings("keys", keys))
	
	if len(keys) > 0 && keys[0] == "test_resource" {
		logger.Info("✅ Test PASSED: ListAllKeys functionality works")
	} else {
		logger.Error("❌ Test FAILED: ListAllKeys does not return expected keys")
	}

	logger.Info("DHT tests completed")
	
	// 等待用户输入以结束程序
	fmt.Println("\nPress Enter to exit...")
	fmt.Scanln()
}
