package main

import (
	"context"
	"fmt"
	"io/ioutil"
	"lumen_link/internal/core/dht"
	"lumen_link/internal/core/dht/factory"
	"lumen_link/internal/core/transfer"
	"lumen_link/pkg/config"
	"lumen_link/pkg/types"
	"lumen_link/pkg/utils"
	"os"
	"path/filepath"
	"time"

	"go.uber.org/zap"
)

func main() {
	// 创建logger
	logger, _ := zap.NewDevelopment()
	defer logger.Sync()

	// 创建上下文
	ctx, cancel := context.WithCancel(context.Background())
	defer cancel()

	// 加载配置
	cfg, err := config.LoadConfig("test_config.yaml")
	if err != nil {
		logger.Fatal("Failed to load config", zap.Error(err))
	}

	// 设置全局哈希算法
	algo := cfg.Security.GetHashAlgorithm()
	if err := utils.SetGlobalHashAlgorithm(algo); err != nil {
		logger.Warn("Unsupported hash algorithm, using default instead", zap.String("algorithm", algo), zap.Error(err))
	}

	// 创建测试目录
	testDir := "./test_data"
	os.MkdirAll(testDir, 0755)
	defer os.RemoveAll(testDir)

	// 创建测试文件
	testFilePath := filepath.Join(testDir, "test_file.txt")
	testContent := "This is a test file for DHT transfer testing."
	err = ioutil.WriteFile(testFilePath, []byte(testContent), 0644)
	if err != nil {
		logger.Fatal("Failed to create test file", zap.Error(err))
	}

	logger.Info("Created test file", zap.String("path", testFilePath))

	// 创建DHT工厂
	dhtFactory := factory.NewFactory(logger)

	// 创建第一个节点的数据目录
	node1Dir := filepath.Join(testDir, "node1")
	os.MkdirAll(node1Dir, 0755)

	// 创建第二个节点的数据目录
	node2Dir := filepath.Join(testDir, "node2")
	os.MkdirAll(node2Dir, 0755)

	// 创建第一个DHT节点
	logger.Info("Creating first DHT node...")
	dhtNode1, err := dhtFactory.CreateDHTWithEnhancedFeatures(ctx, cfg)
	if err != nil {
		logger.Fatal("Failed to create first DHT node", zap.Error(err))
	}

	// 获取基础DHT
	baseDHT1, ok := dhtNode1.BaseDHT.(*dht.DHT)
	if !ok {
		logger.Fatal("Failed to get base DHT for first node")
	}

	// 启动第一个DHT服务
	err = baseDHT1.Start(ctx, "127.0.0.1:9001", cfg)
	if err != nil {
		logger.Fatal("Failed to start first DHT service", zap.Error(err))
	}
	defer baseDHT1.Stop()

	logger.Info("First DHT node started", zap.String("nodeID", fmt.Sprintf("%x", baseDHT1.GetLocalNode().ID)))

	// 创建第一个节点的传输管理器
	transferManager1, err := transfer.NewTransferManager(node1Dir, cfg, dhtNode1, logger.Named("transfer_manager_1"))
	if err != nil {
		logger.Fatal("Failed to create transfer manager for first node", zap.Error(err))
	}

	// 创建第二个DHT节点
	logger.Info("Creating second DHT node...")
	dhtNode2, err := dhtFactory.CreateDHTWithEnhancedFeatures(ctx, cfg)
	if err != nil {
		logger.Fatal("Failed to create second DHT node", zap.Error(err))
	}

	// 获取基础DHT
	baseDHT2, ok := dhtNode2.BaseDHT.(*dht.DHT)
	if !ok {
		logger.Fatal("Failed to get base DHT for second node")
	}

	// 启动第二个DHT服务
	err = baseDHT2.Start(ctx, "127.0.0.1:9002", cfg)
	if err != nil {
		logger.Fatal("Failed to start second DHT service", zap.Error(err))
	}
	defer baseDHT2.Stop()

	logger.Info("Second DHT node started", zap.String("nodeID", fmt.Sprintf("%x", baseDHT2.GetLocalNode().ID)))

	// 创建第二个节点的传输管理器
	transferManager2, err := transfer.NewTransferManager(node2Dir, cfg, dhtNode2, logger.Named("transfer_manager_2"))
	if err != nil {
		logger.Fatal("Failed to create transfer manager for second node", zap.Error(err))
	}

	// 将第二个节点添加到第一个节点的路由表中
	baseDHT1.GetRoutingTable().Update(baseDHT2.GetLocalNode())
	logger.Info("Added second node to first node's routing table")

	// 将第一个节点添加到第二个节点的路由表中
	baseDHT2.GetRoutingTable().Update(baseDHT1.GetLocalNode())
	logger.Info("Added first node to second node's routing table")

	// 第一个节点上传文件（做种）
	logger.Info("Node 1 uploading file (seeding)...")
	uploadReq := types.UploadRequest{
		SourcePath: testFilePath,
	}
	
	transfer1, err := transferManager1.Upload(ctx, uploadReq)
	if err != nil {
		logger.Fatal("Failed to upload file", zap.Error(err))
	}

	// 等待上传完成
	for transfer1.Status() != "completed" {
		logger.Info("Waiting for upload to complete", zap.String("status", transfer1.Status()))
		time.Sleep(1 * time.Second)
		if transfer1.Status() == "failed" {
			logger.Fatal("Upload failed", zap.Error(transfer1.Error()))
		}
	}

	contentAddr := transfer1.ContentAddress()
	logger.Info("Upload completed", zap.String("contentAddr", contentAddr.String()))

	// 添加提供者信息到DHT
	provider := &dht.Provider{
		ID:             baseDHT1.GetLocalNode().ID,
		Address:        baseDHT1.GetLocalNode().Address,
		LastSeen:       time.Now().Unix(),
		StabilityScore: 1.0,
	}
	
	baseDHT1.AddProvider(contentAddr.String(), provider)
	logger.Info("Provider added for content", zap.String("contentAddr", contentAddr.String()))

	// 等待提供者信息传播
	time.Sleep(2 * time.Second)

	// 第二个节点查找提供者
	logger.Info("Node 2 finding providers...")
	providers, err := baseDHT2.FindProviders(ctx, contentAddr.String())
	if err != nil {
		logger.Error("Failed to find providers", zap.Error(err))
	} else {
		logger.Info("Found providers", zap.Int("count", len(providers)))
		for i, p := range providers {
			logger.Info(fmt.Sprintf("Provider %d", i+1), 
				zap.String("id", fmt.Sprintf("%x", p.ID)),
				zap.String("address", p.Address.String()))
		}
	}

	// 第二个节点下载文件
	logger.Info("Node 2 downloading file...")
	downloadReq := types.DownloadRequest{
		ContentAddr: contentAddr,
		TargetPath:  filepath.Join(node2Dir, "downloaded_file.txt"),
	}
	
	transfer2, err := transferManager2.Download(ctx, downloadReq)
	if err != nil {
		logger.Fatal("Failed to start download", zap.Error(err))
	}

	// 等待下载完成
	for transfer2.Status() != "completed" {
		logger.Info("Waiting for download to complete", zap.String("status", transfer2.Status()))
		time.Sleep(1 * time.Second)
		if transfer2.Status() == "failed" {
			logger.Fatal("Download failed", zap.Error(transfer2.Error()))
		}
	}

	logger.Info("Download completed")

	// 验证下载的文件内容
	downloadedContent, err := ioutil.ReadFile(downloadReq.TargetPath)
	if err != nil {
		logger.Fatal("Failed to read downloaded file", zap.Error(err))
	}

	if string(downloadedContent) == testContent {
		logger.Info("✅ Test PASSED: Downloaded content matches original content")
	} else {
		logger.Error("❌ Test FAILED: Downloaded content does not match original content")
	}

	// 测试第二个节点是否自动开始做种
	time.Sleep(2 * time.Second)
	
	logger.Info("Checking if Node 2 is seeding...")
	// 这里应该有检查第二个节点是否在做种的代码
	// 由于具体实现可能需要访问内部状态，这里只是示意

	logger.Info("DHT transfer test completed")
	
	// 等待用户输入以结束程序
	fmt.Println("\nPress Enter to exit...")
	fmt.Scanln()
}
