// Code generated by command: go run src.go -out ../amd64.s -stubs ../stubs_amd64.go -pkg common. DO NOT EDIT.

//go:build amd64 && !purego

package common

//go:noescape
func addAVX2(p *[256]int16, a *[256]int16, b *[256]int16)

//go:noescape
func subAVX2(p *[256]int16, a *[256]int16, b *[256]int16)

//go:noescape
func nttAVX2(p *[256]int16)

//go:noescape
func invNttAVX2(p *[256]int16)

//go:noescape
func mulHatAVX2(p *[256]int16, a *[256]int16, b *[256]int16)

//go:noescape
func detangleAVX2(p *[256]int16)

//go:noescape
func tangleAVX2(p *[256]int16)

//go:noescape
func barrettReduceAVX2(p *[256]int16)

//go:noescape
func normalizeAVX2(p *[256]int16)
