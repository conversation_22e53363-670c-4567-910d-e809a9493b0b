# Lumen Link 测试覆盖率提升计划

## 1. 总体目标

- 核心模块（internal/core/）测试覆盖率达到 80%+
- 公共包（pkg/）测试覆盖率达到 90%+
- 命令行工具（cmd/）保持 100% 覆盖率
- 整体项目测试覆盖率达到 70%+

## 2. 当前测试覆盖率情况

```
lumen_link/cmd/lumenlinkcli     100.0% (已达标)
lumen_link/cmd/lumenlinkd       100.0% (已达标)
lumen_link/internal/core/dht/kademlia   60.9% (需提升)
lumen_link/internal/core/protocol/compat        9.8% (急需提升)
lumen_link/internal/core/storage        31.0% (急需提升)
lumen_link/internal/core/transport      17.7% (急需提升)
lumen_link/pkg/types    47.4% (需提升)
lumen_link/pkg/utils    80.5% (接近达标)
```

## 3. 优先级排序

1. **高优先级**：
   - internal/core/protocol/compat (9.8%)
   - internal/core/transport (17.7%)
   - internal/core/storage (31.0%)

2. **中优先级**：
   - pkg/types (47.4%)
   - internal/core/dht/kademlia (60.9%)

3. **低优先级**：
   - 其他未测试的子模块

## 4. 详细实施计划

### 4.1 BitTorrent 兼容层测试 (internal/core/protocol/compat)

#### 目标覆盖率：80%+

1. **创建模拟环境**
   - 实现模拟的 BitTorrent 客户端接口
   - 创建测试用的种子文件和磁力链接

2. **测试用例**
   - `TestBitTorrentCompat_ImportFromURI`：测试从磁力链接导入内容
   - `TestBitTorrentCompat_ImportFromFile`：测试从种子文件导入内容
   - `TestBitTorrentCompat_ExportToURI`：测试导出为磁力链接
   - `TestBitTorrentCompat_ExportToFile`：测试导出为种子文件
   - `TestCompatRegistry`：测试兼容层注册表功能

3. **模拟外部依赖**
   - 使用 `exec.Command` 的模拟版本替代真实的命令执行
   - 创建虚拟文件系统进行测试

### 4.2 传输层测试 (internal/core/transport)

#### 目标覆盖率：80%+

1. **完善现有测试**
   - 扩展 `quic_server_test.go` 和 `quic_client_test.go` 的测试用例
   - 添加错误处理和边界条件测试

2. **新增测试用例**
   - `TestProtocolRegistry`：测试协议注册表功能
   - `TestStreamMultiplexer_AcceptStream`：测试流接收功能
   - `TestQuicConnection_Timeout`：测试连接超时处理
   - `TestQuicConnection_Reconnect`：测试重连功能

3. **测试网络故障场景**
   - 模拟网络延迟和丢包
   - 测试连接中断恢复

### 4.3 存储层测试 (internal/core/storage)

#### 目标覆盖率：80%+

1. **子模块测试**
   - `address`：测试内容寻址功能
   - `cache`：测试缓存机制
   - `chunk`：测试文件分片功能
   - `distributed`：测试分布式存储
   - `local`：测试本地存储
   - `metadata`：测试元数据管理
   - `middleware`：测试中间件功能

2. **集成测试**
   - 测试存储层与 DHT 的交互
   - 测试缓存与本地存储的协作
   - 测试分布式存储的数据一致性

3. **性能测试**
   - 测试大文件处理性能
   - 测试并发读写性能
   - 测试缓存命中率

### 4.4 类型包测试 (pkg/types)

#### 目标覆盖率：90%+

1. **完善现有测试**
   - 添加边界条件测试
   - 添加错误处理测试

2. **新增测试用例**
   - 测试所有类型的序列化和反序列化
   - 测试类型转换函数
   - 测试验证函数

### 4.5 Kademlia DHT 测试 (internal/core/dht/kademlia)

#### 目标覆盖率：80%+

1. **完善现有测试**
   - 扩展节点查找测试
   - 添加路由表管理测试

2. **新增测试用例**
   - `TestSW_DBG`：测试滑动窗口 De Bruijn 图预测
   - `TestInterlacedMechanism`：测试 Interlaced 机制
   - `TestStabilityScore`：测试节点稳定性评分
   - `TestNodeSelection`：测试节点选择策略

## 5. 测试工具和基础设施

1. **测试辅助工具**
   - 创建通用的测试辅助函数库
   - 实现更多的模拟对象和接口

2. **CI 集成**
   - 配置 GitHub Actions 运行测试并生成覆盖率报告
   - 设置覆盖率阈值检查

3. **测试文档**
   - 编写测试指南，说明如何编写高质量的测试
   - 记录测试模式和最佳实践

## 6. 时间表

### 第一阶段（2周）
- 完成 BitTorrent 兼容层测试
- 完成传输层基础测试
- 设置 CI 测试覆盖率报告

### 第二阶段（2周）
- 完成存储层核心功能测试
- 完成类型包测试
- 开始 Kademlia DHT 测试

### 第三阶段（2周）
- 完成 Kademlia DHT 测试
- 完成存储层高级功能测试
- 完成传输层高级功能测试

### 第四阶段（1周）
- 覆盖剩余未测试模块
- 优化测试性能
- 完善测试文档

## 7. 测试质量保证

1. **代码审查**
   - 所有测试代码必须经过代码审查
   - 确保测试覆盖关键路径和边界条件

2. **测试标准**
   - 每个函数至少有一个正向测试和一个错误处理测试
   - 复杂函数需要测试所有分支路径
   - 公共 API 需要文档测试

3. **持续监控**
   - 定期运行测试覆盖率报告
   - 防止测试覆盖率下降

## 8. 责任分工

- 核心开发者：负责编写核心模块测试
- 质量保证：负责审查测试代码和监控覆盖率
- 所有开发者：遵循"先测试，后实现"的原则

## 9. 成功标准

- 所有高优先级模块达到 80%+ 覆盖率
- 所有中优先级模块达到 70%+ 覆盖率
- 整体项目测试覆盖率达到 70%+
- 所有关键功能和边界条件都有测试覆盖
