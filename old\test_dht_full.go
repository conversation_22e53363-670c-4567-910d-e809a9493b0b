package main

import (
	"context"
	"fmt"
	"io/ioutil"
	"lumen_link/internal/core/dht"
	"lumen_link/internal/core/dht/factory"
	"lumen_link/internal/core/dht/interfaces"
	"lumen_link/pkg/config"
	"lumen_link/pkg/types"
	"lumen_link/pkg/utils"
	"net"
	"os"
	"path/filepath"
	"time"

	"go.uber.org/zap"
)

// SimpleProviderManager 简单的提供者管理器实现
type SimpleProviderManager struct {
	dht *dht.DHT
}

func NewSimpleProviderManager(dht *dht.DHT) *SimpleProviderManager {
	return &SimpleProviderManager{dht: dht}
}

func (m *SimpleProviderManager) AddProvider(ctx context.Context, contentID string, provider interfaces.Provider) error {
	m.dht.AddProvider(contentID, &provider)
	return nil
}

func (m *SimpleProviderManager) FindProviders(ctx context.Context, contentID string) ([]interfaces.Provider, error) {
	return m.dht.FindProviders(ctx, contentID)
}

func (m *SimpleProviderManager) GetProviderByID(ctx context.Context, contentID string, providerID []byte) (*interfaces.Provider, error) {
	providers, err := m.dht.FindProviders(ctx, contentID)
	if err != nil {
		return nil, err
	}

	for i, p := range providers {
		if string(p.ID) == string(providerID) {
			return &providers[i], nil
		}
	}

	return nil, fmt.Errorf("provider not found")
}

func (m *SimpleProviderManager) UpdateProviderScore(ctx context.Context, contentID string, providerID []byte, score float32) error {
	return m.dht.UpdateProvider(providerID, contentID, time.Now().Unix(), score)
}

func (m *SimpleProviderManager) GetProviders(ctx context.Context, contentAddr *types.ContentAddress) ([]interfaces.Provider, error) {
	// 简单实现，返回空列表
	return []interfaces.Provider{}, nil
}

func main() {
	// 创建logger
	logger, _ := zap.NewDevelopment()
	defer logger.Sync()

	// 创建上下文
	ctx, cancel := context.WithCancel(context.Background())
	defer cancel()

	// 加载配置
	cfg := config.DefaultConfig()
	cfg.Network.Transfer.SeedingEnabled = true
	cfg.Network.Transfer.DownloadEnabled = true
	cfg.Storage.Content.StorageNodeEnabled = true

	// 设置全局哈希算法
	algo := cfg.Security.GetHashAlgorithm()
	if err := utils.SetGlobalHashAlgorithm(algo); err != nil {
		logger.Warn("Unsupported hash algorithm, using default instead", zap.String("algorithm", algo), zap.Error(err))
	}

	// 创建测试目录
	testDir := "./test_data"
	os.MkdirAll(testDir, 0755)
	defer os.RemoveAll(testDir)

	// 创建测试文件
	testFilePath := filepath.Join(testDir, "test_file.txt")
	testContent := "This is a test file for DHT transfer testing."
	err := ioutil.WriteFile(testFilePath, []byte(testContent), 0644)
	if err != nil {
		logger.Fatal("Failed to create test file", zap.Error(err))
	}

	logger.Info("Created test file", zap.String("path", testFilePath))

	// 创建DHT工厂
	dhtFactory := factory.NewFactory(logger)

	// 创建第一个节点的数据目录
	node1Dir := filepath.Join(testDir, "node1")
	os.MkdirAll(node1Dir, 0755)

	// 创建第二个节点的数据目录
	node2Dir := filepath.Join(testDir, "node2")
	os.MkdirAll(node2Dir, 0755)

	// 创建第一个DHT节点
	logger.Info("Creating first DHT node...")
	dhtNode1, err := dhtFactory.CreateDHTWithEnhancedFeatures(ctx, cfg)
	if err != nil {
		logger.Fatal("Failed to create first DHT node", zap.Error(err))
	}

	// 获取基础DHT
	baseDHT1, ok := dhtNode1.BaseDHT.(*dht.DHT)
	if !ok {
		logger.Fatal("Failed to get base DHT for first node")
	}

	// 设置节点地址
	node1Addr := &net.TCPAddr{IP: net.ParseIP("127.0.0.1"), Port: 9001}
	baseDHT1.GetLocalNode().Address = node1Addr

	// 启动第一个DHT服务
	err = baseDHT1.Start(ctx, "127.0.0.1:9001", cfg)
	if err != nil {
		logger.Fatal("Failed to start first DHT service", zap.Error(err))
	}
	defer baseDHT1.Stop()

	logger.Info("First DHT node started", zap.String("nodeID", fmt.Sprintf("%x", baseDHT1.GetLocalNode().ID)))

	// 创建第一个节点的提供者管理器
	providerManager1 := NewSimpleProviderManager(baseDHT1)

	// 创建第二个DHT节点
	logger.Info("Creating second DHT node...")
	dhtNode2, err := dhtFactory.CreateDHTWithEnhancedFeatures(ctx, cfg)
	if err != nil {
		logger.Fatal("Failed to create second DHT node", zap.Error(err))
	}

	// 获取基础DHT
	baseDHT2, ok := dhtNode2.BaseDHT.(*dht.DHT)
	if !ok {
		logger.Fatal("Failed to get base DHT for second node")
	}

	// 设置节点地址
	node2Addr := &net.TCPAddr{IP: net.ParseIP("127.0.0.1"), Port: 9002}
	baseDHT2.GetLocalNode().Address = node2Addr

	// 启动第二个DHT服务
	err = baseDHT2.Start(ctx, "127.0.0.1:9002", cfg)
	if err != nil {
		logger.Fatal("Failed to start second DHT service", zap.Error(err))
	}
	defer baseDHT2.Stop()

	logger.Info("Second DHT node started", zap.String("nodeID", fmt.Sprintf("%x", baseDHT2.GetLocalNode().ID)))

	// 创建第二个节点的提供者管理器
	providerManager2 := NewSimpleProviderManager(baseDHT2)

	// 测试提供者功能
	logger.Info("Testing provider functionality...")

	// 添加提供者信息到DHT
	provider := interfaces.Provider{
		ID:             baseDHT1.GetLocalNode().ID,
		Address:        baseDHT1.GetLocalNode().Address,
		LastSeen:       time.Now().Unix(),
		StabilityScore: 1.0,
	}

	contentKey := "test-content-address"

	err = providerManager1.AddProvider(ctx, contentKey, provider)
	if err != nil {
		logger.Error("Failed to add provider", zap.Error(err))
	} else {
		logger.Info("Provider added for content", zap.String("contentKey", contentKey))
	}

	// 等待提供者信息传播
	logger.Info("Waiting for provider information to propagate...")
	time.Sleep(2 * time.Second)

	// 手动将第一个节点的提供者信息添加到第二个节点
	logger.Info("Manually adding provider information from first node to second node...")
	baseDHT2.AddProvider(contentKey, &interfaces.Provider{
		ID:             baseDHT1.GetLocalNode().ID,
		Address:        baseDHT1.GetLocalNode().Address,
		LastSeen:       time.Now().Unix(),
		StabilityScore: 1.0,
	})

	// 检查第一个节点的提供者信息
	logger.Info("Checking providers on first node...")
	node1Providers := baseDHT1.GetLocalProviders(contentKey)
	logger.Info("Local providers on first node", zap.Int("count", len(node1Providers)))
	for i, p := range node1Providers {
		if p == nil {
			logger.Warn("Provider is nil")
			continue
		}
		logger.Info(fmt.Sprintf("Provider %d", i+1),
			zap.String("id", fmt.Sprintf("%x", p.ID)))
		if p.Address != nil {
			logger.Info(fmt.Sprintf("Provider %d address", i+1),
				zap.String("address", p.Address.String()))
		} else {
			logger.Warn(fmt.Sprintf("Provider %d has nil address", i+1))
		}
	}

	// 第二个节点查找提供者
	logger.Info("Node 2 finding providers...")
	providers, err := providerManager2.FindProviders(ctx, contentKey)
	if err != nil {
		logger.Error("Failed to find providers", zap.Error(err))
	} else {
		logger.Info("Found providers", zap.Int("count", len(providers)))
		for i, p := range providers {
			logger.Info(fmt.Sprintf("Provider %d", i+1),
				zap.String("id", fmt.Sprintf("%x", p.ID)))
			if p.Address != nil {
				logger.Info(fmt.Sprintf("Provider %d address", i+1),
					zap.String("address", p.Address.String()))
			} else {
				logger.Warn(fmt.Sprintf("Provider %d has nil address", i+1))
			}
		}

		if len(providers) > 0 {
			logger.Info("✅ Test PASSED: Provider functionality works")
		} else {
			logger.Error("❌ Test FAILED: No providers found")
		}
	}

	logger.Info("DHT test completed")

	// 等待用户输入以结束程序
	fmt.Println("\nPress Enter to exit...")
	fmt.Scanln()
}
