# Lumen Link Rust 重构开发文档

## 1. 项目概述

Lumen Link 是一个高性能分布式区块链系统，目前主要使用 Go 语言实现，部分性能关键模块使用 Rust。本重构项目旨在将整个系统迁移到 Rust 语言，并基于 libp2p 生态系统进行重新设计，以获得更好的性能、内存安全性和互操作性。

### 1.1 项目目标

- 完全迁移到 Rust 语言，消除 Go 与 Rust 之间的 FFI 开销
- 利用 libp2p 生态系统提供的成熟组件
- 保持现有功能和 API 兼容性
- 提高系统整体性能和资源利用率
- 增强与其他 libp2p 网络的互操作性
- 改进代码可维护性和可测试性

### 1.2 核心功能

- 分布式哈希表 (DHT) 用于内容寻址和节点发现
- 区块链层用于元数据存储和完整性验证
- 高性能 P2P 网络传输层
- 分布式存储系统
- BitTorrent 兼容层
- 后量子密码学安全机制

## 2. 技术栈

### 2.1 主要依赖

| 组件 | 技术选择 | 版本 |
|------|----------|------|
| 语言 | Rust | 1.70+ |
| 异步运行时 | Tokio | 1.28+ |
| P2P 网络库 | libp2p | 0.51+ |
| 存储引擎 | sled / RocksDB | 0.34+ / 0.20+ |
| 序列化 | Serde / Prost | 1.0+ / 0.11+ |
| API 框架 | Tonic / Actix-web | 0.9+ / 4.3+ |
| 密码学 | RustCrypto / pqcrypto | 最新版 |
| 日志 | tracing | 0.1+ |
| 配置 | config | 0.13+ |
| CLI | clap | 4.2+ |

### 2.2 libp2p 组件

- **libp2p-core**: 核心抽象和接口
- **libp2p-kad-dht**: Kademlia DHT 实现
- **libp2p-quic**: QUIC 传输协议
- **libp2p-noise**: 加密传输
- **libp2p-mplex/yamux**: 多路复用
- **libp2p-floodsub/gossipsub**: 发布/订阅
- **libp2p-identify**: 节点识别
- **libp2p-mdns**: 本地网络发现

## 3. 系统架构

### 3.1 整体架构

Lumen Link 采用分层架构设计，主要包括以下几层：

1. **网络层**: 负责 P2P 通信，基于 libp2p
2. **DHT 层**: 负责内容寻址和节点发现
3. **区块链层**: 负责元数据存储和完整性验证
4. **存储层**: 负责数据持久化和分布式存储
5. **API 层**: 提供外部接口

### 3.2 组件关系图

```
+----------------+    +----------------+    +----------------+
|     客户端      |    |    命令行工具    |    |     Web UI     |
+-------+--------+    +-------+--------+    +-------+--------+
        |                     |                     |
        v                     v                     v
+-------+---------------------+---------------------+--------+
|                           API 层                           |
+-------+---------------------+---------------------+--------+
        |                     |                     |
        v                     v                     v
+----------------+    +----------------+    +----------------+
|    区块链层     |    |     DHT 层     |    |  BitTorrent层  |
+-------+--------+    +-------+--------+    +-------+--------+
        |                     |                     |
        v                     v                     v
+-------+---------------------+---------------------+--------+
|                          存储层                            |
+----------------------------------------------------------------+
|                          网络层 (libp2p)                       |
+----------------------------------------------------------------+
```

### 3.3 数据流

1. **内容发布流程**:
   - 用户通过 API 提交内容
   - 内容被分片并存储在本地存储
   - 内容元数据被写入区块链
   - 内容哈希被发布到 DHT
   - 节点开始为内容提供服务

2. **内容检索流程**:
   - 用户请求内容哈希
   - 系统通过 DHT 查找内容提供者
   - 系统从提供者下载内容分片
   - 系统验证内容完整性
   - 系统将内容返回给用户

## 4. 模块设计

### 4.1 网络模块

网络模块基于 libp2p 实现，负责节点间的通信。

#### 4.1.1 主要组件

- **Transport**: 负责底层传输，支持 QUIC、TCP+Noise 等
- **Behaviour**: 定义节点网络行为，包括 DHT、发布/订阅等
- **Swarm**: 管理连接和协议处理
- **Protocol**: 自定义协议实现

#### 4.1.2 关键接口

```rust
// 网络服务接口
pub trait NetworkService: Send + Sync + 'static {
    // 启动网络服务
    async fn start(&self) -> Result<(), NetworkError>;
    
    // 停止网络服务
    async fn stop(&self) -> Result<(), NetworkError>;
    
    // 连接到对等节点
    async fn connect(&self, addr: Multiaddr) -> Result<(), NetworkError>;
    
    // 发布消息到主题
    async fn publish(&self, topic: &str, data: Vec<u8>) -> Result<(), NetworkError>;
    
    // 订阅主题
    async fn subscribe(&self, topic: &str) -> Result<(), NetworkError>;
    
    // 提供内容
    async fn provide(&self, content_id: &[u8]) -> Result<(), NetworkError>;
    
    // 查找内容提供者
    async fn find_providers(&self, content_id: &[u8]) -> Result<Vec<PeerId>, NetworkError>;
}
```

### 4.2 DHT 模块

DHT 模块基于 libp2p-kad-dht 实现，负责内容寻址和节点发现。

#### 4.2.1 主要组件

- **KademliaService**: DHT 服务实现
- **Provider**: 内容提供者管理
- **Record**: DHT 记录管理
- **Proximity**: 节点距离计算和路由表优化

#### 4.2.2 关键接口

```rust
// DHT 服务接口
pub trait DhtService: Send + Sync + 'static {
    // 启动 DHT 服务
    async fn start(&self) -> Result<(), DhtError>;
    
    // 停止 DHT 服务
    async fn stop(&self) -> Result<(), DhtError>;
    
    // 存储值
    async fn put_value(&self, key: &[u8], value: Vec<u8>) -> Result<(), DhtError>;
    
    // 获取值
    async fn get_value(&self, key: &[u8]) -> Result<Option<Vec<u8>>, DhtError>;
    
    // 提供内容
    async fn provide(&self, content_id: &[u8]) -> Result<(), DhtError>;
    
    // 查找内容提供者
    async fn find_providers(&self, content_id: &[u8]) -> Result<Vec<PeerId>, DhtError>;
    
    // 查找最近的节点
    async fn find_closest_peers(&self, key: &[u8]) -> Result<Vec<PeerId>, DhtError>;
}
```

### 4.3 区块链模块

区块链模块实现轻量级区块链，用于元数据存储和完整性验证。

#### 4.3.1 主要组件

- **Block**: 区块定义
- **Chain**: 链管理
- **Consensus**: 共识算法
- **Transaction**: 交易处理
- **State**: 状态管理

#### 4.3.2 关键接口

```rust
// 区块链服务接口
pub trait BlockchainService: Send + Sync + 'static {
    // 启动区块链服务
    async fn start(&self) -> Result<(), BlockchainError>;
    
    // 停止区块链服务
    async fn stop(&self) -> Result<(), BlockchainError>;
    
    // 提交交易
    async fn submit_transaction(&self, tx: Transaction) -> Result<TxHash, BlockchainError>;
    
    // 获取交易
    async fn get_transaction(&self, tx_hash: &TxHash) -> Result<Option<Transaction>, BlockchainError>;
    
    // 获取区块
    async fn get_block(&self, block_hash: &BlockHash) -> Result<Option<Block>, BlockchainError>;
    
    // 获取最新区块
    async fn get_latest_block(&self) -> Result<Block, BlockchainError>;
    
    // 获取区块链状态
    async fn get_state(&self) -> Result<ChainState, BlockchainError>;
}
```

### 4.4 存储模块

存储模块负责数据持久化和分布式存储。

#### 4.4.1 主要组件

- **LocalStorage**: 本地存储实现
- **DistributedStorage**: 分布式存储实现
- **ContentAddressable**: 内容寻址存储
- **Chunker**: 数据分片
- **Cache**: 缓存管理

#### 4.4.2 关键接口

```rust
// 存储服务接口
pub trait StorageService: Send + Sync + 'static {
    // 存储数据
    async fn put(&self, data: Vec<u8>) -> Result<ContentId, StorageError>;
    
    // 获取数据
    async fn get(&self, content_id: &ContentId) -> Result<Option<Vec<u8>>, StorageError>;
    
    // 删除数据
    async fn delete(&self, content_id: &ContentId) -> Result<(), StorageError>;
    
    // 检查数据是否存在
    async fn contains(&self, content_id: &ContentId) -> Result<bool, StorageError>;
    
    // 获取数据大小
    async fn size(&self, content_id: &ContentId) -> Result<Option<usize>, StorageError>;
    
    // 列出所有内容
    async fn list(&self) -> Result<Vec<ContentId>, StorageError>;
}
```

### 4.5 API 模块

API 模块提供外部接口，包括 gRPC 和 REST API。

#### 4.5.1 主要组件

- **GrpcService**: gRPC 服务实现
- **RestService**: REST API 实现
- **JsonRpcService**: JSON-RPC 服务实现
- **WebSocketService**: WebSocket 服务实现

#### 4.5.2 关键接口

```rust
// API 服务接口
pub trait ApiService: Send + Sync + 'static {
    // 启动 API 服务
    async fn start(&self) -> Result<(), ApiError>;
    
    // 停止 API 服务
    async fn stop(&self) -> Result<(), ApiError>;
    
    // 获取服务地址
    fn address(&self) -> String;
    
    // 获取服务状态
    async fn status(&self) -> Result<ServiceStatus, ApiError>;
}
```

## 5. 数据模型

### 5.1 核心数据结构

#### 5.1.1 内容标识符

```rust
/// 内容标识符
#[derive(Clone, Debug, PartialEq, Eq, Hash, Serialize, Deserialize)]
pub struct ContentId(pub [u8; 32]);

impl ContentId {
    /// 从数据创建内容标识符
    pub fn from_data(data: &[u8]) -> Self {
        let hash = blake3::hash(data);
        Self(hash.into())
    }
    
    /// 从十六进制字符串创建内容标识符
    pub fn from_hex(hex: &str) -> Result<Self, ContentIdError> {
        // 实现从十六进制字符串解析
    }
    
    /// 转换为十六进制字符串
    pub fn to_hex(&self) -> String {
        // 实现转换为十六进制字符串
    }
}
```

#### 5.1.2 区块

```rust
/// 区块头
#[derive(Clone, Debug, Serialize, Deserialize)]
pub struct BlockHeader {
    /// 区块版本
    pub version: u32,
    /// 前一个区块的哈希
    pub prev_block_hash: Option<BlockHash>,
    /// 默克尔树根
    pub merkle_root: Hash,
    /// 时间戳
    pub timestamp: u64,
    /// 难度目标
    pub difficulty: u32,
    /// 随机数
    pub nonce: u64,
}

/// 区块
#[derive(Clone, Debug, Serialize, Deserialize)]
pub struct Block {
    /// 区块头
    pub header: BlockHeader,
    /// 交易列表
    pub transactions: Vec<Transaction>,
}

impl Block {
    /// 计算区块哈希
    pub fn hash(&self) -> BlockHash {
        // 实现区块哈希计算
    }
    
    /// 验证区块
    pub fn validate(&self) -> Result<(), BlockValidationError> {
        // 实现区块验证
    }
}
```

#### 5.1.3 交易

```rust
/// 交易
#[derive(Clone, Debug, Serialize, Deserialize)]
pub struct Transaction {
    /// 交易类型
    pub tx_type: TransactionType,
    /// 发送者
    pub sender: Identity,
    /// 接收者
    pub receiver: Option<Identity>,
    /// 交易数据
    pub data: Vec<u8>,
    /// 时间戳
    pub timestamp: u64,
    /// 签名
    pub signature: Option<Signature>,
}

impl Transaction {
    /// 计算交易哈希
    pub fn hash(&self) -> TxHash {
        // 实现交易哈希计算
    }
    
    /// 签名交易
    pub fn sign(&mut self, identity: &Identity) -> Result<(), TransactionError> {
        // 实现交易签名
    }
    
    /// 验证交易
    pub fn verify(&self) -> Result<(), TransactionError> {
        // 实现交易验证
    }
}
```

#### 5.1.4 身份

```rust
/// 身份
#[derive(Clone, Debug, Serialize, Deserialize)]
pub struct Identity {
    /// 公钥
    pub public_key: PublicKey,
    /// 节点 ID
    pub peer_id: PeerId,
}

impl Identity {
    /// 创建新身份
    pub fn new() -> (Self, SecretKey) {
        // 实现身份创建
    }
    
    /// 从密钥创建身份
    pub fn from_keypair(keypair: &Keypair) -> Self {
        // 实现从密钥创建身份
    }
    
    /// 签名数据
    pub fn sign(&self, secret_key: &SecretKey, data: &[u8]) -> Result<Signature, IdentityError> {
        // 实现数据签名
    }
    
    /// 验证签名
    pub fn verify(&self, data: &[u8], signature: &Signature) -> Result<bool, IdentityError> {
        // 实现签名验证
    }
}
```

## 6. 实现计划

### 6.1 阶段划分

#### 6.1.1 阶段一：基础设施 (1-2个月)

- 搭建项目框架
- 实现配置系统
- 实现日志系统
- 实现基本的网络层
- 实现基本的存储层
- 编写单元测试

#### 6.1.2 阶段二：核心功能 (2-3个月)

- 实现完整的 DHT 功能
- 实现区块链核心
- 实现分布式存储
- 实现基本的 API
- 编写集成测试

#### 6.1.3 阶段三：高级功能 (2-3个月)

- 实现 BitTorrent 兼容层
- 实现后量子密码学
- 优化性能
- 增强安全性
- 编写系统测试

#### 6.1.4 阶段四：完善和发布 (1-2个月)

- 完善文档
- 修复 bug
- 性能调优
- 安全审计
- 发布 1.0 版本

### 6.2 里程碑

1. **M1**: 基础框架完成 (1个月)
2. **M2**: 核心功能完成 (3个月)
3. **M3**: 高级功能完成 (6个月)
4. **M4**: 1.0 版本发布 (8个月)

## 7. 测试策略

### 7.1 测试类型

- **单元测试**: 测试各个组件的独立功能
- **集成测试**: 测试组件之间的交互
- **系统测试**: 测试整个系统的功能
- **性能测试**: 测试系统的性能和可扩展性
- **安全测试**: 测试系统的安全性

### 7.2 测试工具

- **Rust 测试框架**: 用于单元测试和集成测试
- **Tokio 测试工具**: 用于异步代码测试
- **Criterion**: 用于性能基准测试
- **Mocking 框架**: 用于模拟依赖
- **Fuzzing 工具**: 用于模糊测试

### 7.3 测试覆盖率目标

- 单元测试覆盖率: > 80%
- 集成测试覆盖率: > 60%
- 系统测试覆盖率: > 40%

## 8. 部署策略

### 8.1 部署环境

- **开发环境**: 本地开发机器
- **测试环境**: CI/CD 系统
- **预生产环境**: 小规模集群
- **生产环境**: �