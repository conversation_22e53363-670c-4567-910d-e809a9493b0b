package main

import (
	"context"
	"fmt"
	"lumen_link/internal/core/dht"
	"lumen_link/internal/core/dht/factory"
	"lumen_link/internal/core/dht/interfaces"
	"lumen_link/pkg/config"
	"lumen_link/pkg/utils"
	"time"

	"go.uber.org/zap"
)

func main() {
	// 创建logger
	logger, _ := zap.NewDevelopment()
	defer logger.Sync()

	// 创建上下文
	ctx, cancel := context.WithCancel(context.Background())
	defer cancel()

	// 加载配置
	cfg := config.DefaultConfig()
	cfg.Network.Transfer.SeedingEnabled = true
	cfg.Network.Transfer.DownloadEnabled = true
	cfg.Storage.Content.StorageNodeEnabled = true

	// 设置全局哈希算法
	algo := cfg.Security.GetHashAlgorithm()
	if err := utils.SetGlobalHashAlgorithm(algo); err != nil {
		logger.Warn("Unsupported hash algorithm, using default instead", zap.String("algorithm", algo), zap.Error(err))
	}

	// 创建DHT工厂
	dhtFactory := factory.NewFactory(logger)

	// 创建第一个DHT节点
	logger.Info("Creating first DHT node...")
	dhtNode1, err := dhtFactory.CreateDHTWithEnhancedFeatures(ctx, cfg)
	if err != nil {
		logger.Fatal("Failed to create first DHT node", zap.Error(err))
	}

	// 获取基础DHT
	baseDHT1, ok := dhtNode1.BaseDHT.(*dht.DHT)
	if !ok {
		logger.Fatal("Failed to get base DHT for first node")
	}

	// 启动第一个DHT服务
	err = baseDHT1.Start(ctx, "127.0.0.1:9001", cfg)
	if err != nil {
		logger.Fatal("Failed to start first DHT service", zap.Error(err))
	}
	defer baseDHT1.Stop()

	logger.Info("First DHT node started", zap.String("nodeID", fmt.Sprintf("%x", baseDHT1.GetLocalNode().ID)))

	// 创建第二个DHT节点
	logger.Info("Creating second DHT node...")
	dhtNode2, err := dhtFactory.CreateDHTWithEnhancedFeatures(ctx, cfg)
	if err != nil {
		logger.Fatal("Failed to create second DHT node", zap.Error(err))
	}

	// 获取基础DHT
	baseDHT2, ok := dhtNode2.BaseDHT.(*dht.DHT)
	if !ok {
		logger.Fatal("Failed to get base DHT for second node")
	}

	// 启动第二个DHT服务
	err = baseDHT2.Start(ctx, "127.0.0.1:9002", cfg)
	if err != nil {
		logger.Fatal("Failed to start second DHT service", zap.Error(err))
	}
	defer baseDHT2.Stop()

	logger.Info("Second DHT node started", zap.String("nodeID", fmt.Sprintf("%x", baseDHT2.GetLocalNode().ID)))

	// 测试提供者功能
	logger.Info("Testing provider functionality...")

	// 尝试手动建立节点之间的连接
	logger.Info("Attempting to connect nodes...")

	// 添加提供者信息到DHT
	provider := &interfaces.Provider{
		ID:             baseDHT1.GetLocalNode().ID,
		Address:        baseDHT1.GetLocalNode().Address,
		LastSeen:       time.Now().Unix(),
		StabilityScore: 1.0,
	}

	contentKey := "test_content"
	baseDHT1.AddProvider(contentKey, provider)
	logger.Info("Provider added for content", zap.String("contentKey", contentKey))

	// 等待提供者信息传播
	logger.Info("Waiting for provider information to propagate...")
	time.Sleep(5 * time.Second)

	// 检查第一个节点的提供者信息
	logger.Info("Checking providers on first node...")
	node1Providers := baseDHT1.GetLocalProviders(contentKey)
	logger.Info("Local providers on first node", zap.Int("count", len(node1Providers)))
	for i, p := range node1Providers {
		if p == nil {
			logger.Warn("Provider is nil")
			continue
		}
		logger.Info(fmt.Sprintf("Provider %d", i+1),
			zap.String("id", fmt.Sprintf("%x", p.ID)))
		if p.Address != nil {
			logger.Info(fmt.Sprintf("Provider %d address", i+1),
				zap.String("address", p.Address.String()))
		} else {
			logger.Warn(fmt.Sprintf("Provider %d has nil address", i+1))
		}
	}

	// 第二个节点查找提供者
	logger.Info("Node 2 finding providers...")
	providers, err := baseDHT2.FindProviders(ctx, contentKey)
	if err != nil {
		logger.Error("Failed to find providers", zap.Error(err))
	} else {
		logger.Info("Found providers", zap.Int("count", len(providers)))
		for i, p := range providers {
			logger.Info(fmt.Sprintf("Provider %d", i+1),
				zap.String("id", fmt.Sprintf("%x", p.ID)))
			if p.Address != nil {
				logger.Info(fmt.Sprintf("Provider %d address", i+1),
					zap.String("address", p.Address.String()))
			} else {
				logger.Warn(fmt.Sprintf("Provider %d has nil address", i+1))
			}
		}

		if len(providers) > 0 {
			logger.Info("✅ Test PASSED: Provider functionality works")
		} else {
			logger.Error("❌ Test FAILED: No providers found")
		}
	}

	logger.Info("DHT test completed")

	// 等待用户输入以结束程序
	fmt.Println("\nPress Enter to exit...")
	fmt.Scanln()
}
