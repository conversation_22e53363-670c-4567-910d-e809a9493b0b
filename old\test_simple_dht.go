package main

import (
	"context"
	"fmt"
	"io/ioutil"
	"lumen_link/internal/core/dht"
	"lumen_link/internal/core/dht/factory"
	"lumen_link/pkg/config"
	"lumen_link/pkg/utils"
	"os"
	"path/filepath"
	"time"

	"go.uber.org/zap"
)

func main() {
	// 创建logger
	logger, _ := zap.NewDevelopment()
	defer logger.Sync()

	// 创建上下文
	ctx, cancel := context.WithCancel(context.Background())
	defer cancel()

	// 加载配置
	cfg, err := config.LoadConfig("test_config.yaml")
	if err != nil {
		logger.Fatal("Failed to load config", zap.Error(err))
	}

	// 设置全局哈希算法
	algo := cfg.Security.GetHashAlgorithm()
	if err := utils.SetGlobalHashAlgorithm(algo); err != nil {
		logger.Warn("Unsupported hash algorithm, using default instead", zap.String("algorithm", algo), zap.Error(err))
	}

	// 创建测试目录
	testDir := "./test_data"
	os.MkdirAll(testDir, 0755)
	defer os.RemoveAll(testDir)

	// 创建测试文件
	testFilePath := filepath.Join(testDir, "test_file.txt")
	testContent := "This is a test file for DHT transfer testing."
	err = ioutil.WriteFile(testFilePath, []byte(testContent), 0644)
	if err != nil {
		logger.Fatal("Failed to create test file", zap.Error(err))
	}

	logger.Info("Created test file", zap.String("path", testFilePath))

	// 创建DHT工厂
	dhtFactory := factory.NewFactory(logger)

	// 创建第一个DHT节点
	logger.Info("Creating first DHT node...")
	dhtNode1, err := dhtFactory.CreateDHTWithEnhancedFeatures(ctx, cfg)
	if err != nil {
		logger.Fatal("Failed to create first DHT node", zap.Error(err))
	}

	// 获取基础DHT
	baseDHT1, ok := dhtNode1.BaseDHT.(*dht.DHT)
	if !ok {
		logger.Fatal("Failed to get base DHT for first node")
	}

	// 启动第一个DHT服务
	err = baseDHT1.Start(ctx, "127.0.0.1:9001", cfg)
	if err != nil {
		logger.Fatal("Failed to start first DHT service", zap.Error(err))
	}
	defer baseDHT1.Stop()

	logger.Info("First DHT node started", zap.String("nodeID", fmt.Sprintf("%x", baseDHT1.GetLocalNode().ID)))

	// 创建第二个DHT节点
	logger.Info("Creating second DHT node...")
	dhtNode2, err := dhtFactory.CreateDHTWithEnhancedFeatures(ctx, cfg)
	if err != nil {
		logger.Fatal("Failed to create second DHT node", zap.Error(err))
	}

	// 获取基础DHT
	baseDHT2, ok := dhtNode2.BaseDHT.(*dht.DHT)
	if !ok {
		logger.Fatal("Failed to get base DHT for second node")
	}

	// 启动第二个DHT服务
	err = baseDHT2.Start(ctx, "127.0.0.1:9002", cfg)
	if err != nil {
		logger.Fatal("Failed to start second DHT service", zap.Error(err))
	}
	defer baseDHT2.Stop()

	logger.Info("Second DHT node started", zap.String("nodeID", fmt.Sprintf("%x", baseDHT2.GetLocalNode().ID)))

	// 将第二个节点添加到第一个节点的路由表中
	baseDHT1.GetRoutingTable().Update(baseDHT2.GetLocalNode())
	logger.Info("Added second node to first node's routing table")

	// 将第一个节点添加到第二个节点的路由表中
	baseDHT2.GetRoutingTable().Update(baseDHT1.GetLocalNode())
	logger.Info("Added first node to second node's routing table")

	// 测试数据存储和检索
	testKey := []byte("test_content_key")
	testValue := []byte(testContent)

	// 在第一个节点上存储数据
	logger.Info("Storing data on first node...")
	err = baseDHT1.Store(ctx, testKey, testValue)
	if err != nil {
		logger.Error("Failed to store data on first node", zap.Error(err))
	} else {
		logger.Info("Data stored successfully on first node")
	}

	// 添加提供者信息到DHT
	provider := &dht.Provider{
		ID:             baseDHT1.GetLocalNode().ID,
		Address:        baseDHT1.GetLocalNode().Address,
		LastSeen:       time.Now().Unix(),
		StabilityScore: 1.0,
	}

	contentKey := "test_content"
	baseDHT1.AddProvider(contentKey, provider)
	logger.Info("Provider added for content", zap.String("contentKey", contentKey))

	// 等待数据传播
	time.Sleep(2 * time.Second)

	// 从第二个节点检索数据
	logger.Info("Retrieving data from second node...")
	retrievedValue, err := baseDHT2.Retrieve(ctx, testKey)
	if err != nil {
		logger.Error("Failed to retrieve data from second node", zap.Error(err))
	} else {
		logger.Info("Data retrieved successfully from second node",
			zap.String("expected", string(testValue)),
			zap.String("actual", string(retrievedValue)))

		// 验证数据
		if string(retrievedValue) == string(testValue) {
			logger.Info("✅ Test PASSED: Retrieved value matches stored value")
		} else {
			logger.Error("❌ Test FAILED: Retrieved value does not match stored value")
		}
	}

	// 第二个节点查找提供者
	logger.Info("Node 2 finding providers...")
	providers, err := baseDHT2.FindProviders(ctx, contentKey)
	if err != nil {
		logger.Error("Failed to find providers", zap.Error(err))
	} else {
		logger.Info("Found providers", zap.Int("count", len(providers)))
		for i, p := range providers {
			logger.Info(fmt.Sprintf("Provider %d", i+1),
				zap.String("id", fmt.Sprintf("%x", p.ID)),
				zap.String("address", p.Address.String()))
		}

		if len(providers) > 0 {
			logger.Info("✅ Test PASSED: Provider functionality works")
		} else {
			logger.Error("❌ Test FAILED: No providers found")
		}
	}

	logger.Info("DHT test completed")

	// 等待用户输入以结束程序
	fmt.Println("\nPress Enter to exit...")
	fmt.Scanln()
}
